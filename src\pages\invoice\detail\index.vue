<template>
	<Layout
		title="发票详情"
		left-text=""
		show-nav-bar
		layout-background="paymentLinearGradient"
		@left-click="navigateBack"
	>
		<view class="invoice-detail">
			<!-- 状态标题 -->
			<UiTitle :title="titleTips" size="xlg">
				<template #prefix>
					<img :src="titleIcon" class="title-icon" />
				</template>
			</UiTitle>
			<!-- 发票卡片 -->
			<view class="invoice-card">
				<!-- 发票头部 -->
				<view class="invoice-header">
					<img src="@/static/images/invoice/Subtract.png" class="invoice-img" />
					<view class="invoice-title">
						{{ invoiceData.invoiceType === 'normal' ? '增值税电子普通发票' : '增值税电子专用发票' }}
					</view>
				</view>
				<!-- 分割线 -->
				<view class="invoce-divider">
					<view class="left" />
					<view class="center">
						<view class="line" />
					</view>
					<view class="right" />
				</view>

				<!-- 发票内容 -->
				<scroll-view class="invoice-content-wrapper" scroll-y="true">
					<view class="invoice-content">
						<UiCell title="抬头名称" :value="invoiceData.title" size="sm" />
						<UiCell title="公司税号" :value="invoiceData.taxNumber" size="sm" />
						<UiCell title="开票金额" :value="invoiceData.totalAmount" size="sm" />
						<UiCell title="申请时间" :value="invoiceData.createTime" size="sm" />
						<UiCell
							title="包含订单"
							:value="`共${invoiceData.selectedOrders.length}笔  >`"
							size="sm"
							@cellClick="showOrdersPopup"
						/>
					</view>

					<!-- 展开更多 -->
					<view class="expand-section">
						<view class="expand-content" v-if="showMore">
							<UiCell title="开户银行" :value="invoiceData.bankName" size="sm" />
							<UiCell title="银行账户" :value="invoiceData.bankAccount" size="sm" />
							<UiCell title="公司地址" :value="invoiceData.companyAddress" size="sm" />
							<UiCell title="公司电话" :value="invoiceData.telephone" size="sm" />
							<UiCell title="开票备注" :value="invoiceData.remark" size="sm" />
						</view>

						<view class="expand-header" @click="toggleMore">
							<text class="expand-text">{{ showMore ? '收起' : '更多' }}</text>
							<view class="expand-icon">
								<image
									:src="
										showMore
											? '/static/images/invoice/arrow-up-s-fill.png'
											: '/static/images/invoice/arrow-down-s-fill.png'
									"
									class="arrow-icon"
								/>
							</view>
						</view>
					</view>
					<view class="invoice-email">
						<UiCell title="电子邮箱" :value="invoiceData.email" size="sm" />
					</view>
				</scroll-view>
			</view>

			<!-- 订单弹窗 -->
			<UiPopup v-model:show="orderPopupVisible" position="bottom" round>
				<view class="orders-popup">
					<view class="orders-popup-header">
						<text class="orders-popup-title">包含订单</text>
						<image
							src="/static/images/invoice/close.png"
							class="orders-popup-close"
							@click="orderPopupVisible = false"
						/>
					</view>
					<view class="orders-popup-content">
						<view class="order-item" v-for="(order, index) in invoiceData.selectedOrders" :key="index">
							<view class="order-date">{{ order.date }}</view>
							<view class="order-info">
								<view class="order-type">{{ order.type }}</view>
								<view class="order-amount">¥ {{ order.amount }}</view>
							</view>
						</view>
					</view>
					<view class="orders-popup-footer">
						<ui-button type="primary" size="xlg" @click="orderPopupVisible = false">确认</ui-button>
					</view>
				</view>
			</UiPopup>

			<!-- 底部按钮 -->
			<view class="footer-btns">
				<ui-button class="footer-btn" type="normal" size="xlg">重新发送</ui-button>
				<ui-button class="footer-btn" type="normal" size="xlg">查看发票</ui-button>
				<ui-button class="footer-btn" type="normal" size="xlg">下载发票</ui-button>
			</view>
		</view>
	</Layout>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiPopup from '@e-cloud/eslink-plus-uniapp/components/ui-popup/ui-popup.vue'
import Layout from '@/layout/index.vue'
import success from '@/static/payment/reslult/checkbox-circle-fill.png'
import info from '@/static/images/invoice/information-fill.png'

defineOptions({
	name: 'InvoiceDetail',
})

// 发票状态 0 开票中 1 已开票
const status = ref<number>(0)

const titleIcon = status.value === 0 ? info : success

const titleTips = status.value === 0 ? '开票中' : '已开票'

const invoiceData = reactive({
	invoiceType: 'normal', // 发票类型（normal: 普通发票, special: 专用发票）
	titleType: '0', // 发票抬头类型 0：单位，1：个人
	title: '金卡智能集团', // 抬头名称
	taxNumber: '***************', // 统一社会信用代码
	companyAddress: '企业地址信息', // 企业地址
	telephone: '***********', // 手机号码
	bankName: '工商银行', // 开户银行
	bankAccount: 'bk002268951018153544436', // 银行账号
	remark: '发票备注信息', // 备注
	email: '<EMAIL>', // 电子邮箱
	selectedOrders: [
		{
			date: '2025/04/25',
			type: '气费',
			amount: '102.40',
			id: '*********',
		},
	], // 选中的订单
	totalAmount: '102.40', // 总金额
	createTime: '2025/04/25 18:50:09',
})

const showMore = ref(false)
const orderPopupVisible = ref(false)

// 导航函数
const navigateBack = () => {
	uni.navigateBack()
}

// 切换显示更多信息
const toggleMore = () => {
	showMore.value = !showMore.value
}

// 显示订单详情弹窗
const showOrdersPopup = () => {
	console.log('显示订单详情')
	orderPopupVisible.value = true
}
</script>

<style lang="scss" scoped>
.title-icon {
	width: 56rpx;
	height: 56rpx;
}

.invoice-detail {
	display: flex;
	flex-direction: column;
	height: 100%;

	.invoice-card {
		flex: 1;
		display: flex;
		flex-direction: column;
		margin-top: 48rpx;
		border-radius: var(--content-default-radius-md);
		overflow: hidden;
		margin-bottom: 40rpx;

		.invoice-header {
			height: 200rpx;
			padding-top: 8rpx;
			background-color: var(--light-fill-blank, white);
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			flex-shrink: 0;

			.invoice-img {
				position: absolute;
				width: auto;
				height: 152rpx;
				object-fit: cover;
				z-index: 1;
				margin-top: 8rpx;
			}

			.invoice-title {
				font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
				color: #e54545;
				font-size: 40rpx;
				font-weight: 500;
				position: relative;
				z-index: 2;
			}
		}

		.invoce-divider {
			height: 40rpx;
			display: flex;
			flex-direction: row;
			width: 100%;

			.left {
				width: 20rpx;
				height: 40rpx;
				background-color: #fff;
				-webkit-mask-image: radial-gradient(circle at 0 50%, transparent 50%, #fff 51%);
				mask-image: radial-gradient(circle at 0 50%, transparent 50%, #fff 51%);
			}

			.center {
				background-color: var(--light-fill-blank, white);
				flex: 1;
				height: 40rpx;
				display: flex;
				align-items: center;

				.line {
					width: 100%;
					height: 2rpx; /* 线高 */
					background-image: repeating-linear-gradient(
						to right,
						var(--light-border-dark, #dce1ee) 0 8rpx,
						transparent 8rpx 16rpx /* 8 rpx 空白 */
					);
					background-size: 16rpx 2rpx; /* 一个周期 16 rpx（8+8） */
				}
			}

			.right {
				width: 20rpx;
				height: 40rpx;
				background-color: #fff;
				-webkit-mask-image: radial-gradient(circle at 100% 50%, transparent 50%, #fff 51%);
				mask-image: radial-gradient(circle at 100% 50%, transparent 50%, #fff 51%);
			}
		}

		.invoice-content-wrapper {
			flex: 1;

			:deep(.ui-title-title) {
				color: var(--light-text-title);
			}

			:deep(.cell-title) {
				font-size: var(--font-body-md);
				color: var(--light-list-normal-default-text-light);
			}

			:deep(.cell-right) {
				justify-content: flex-start;
			}

			:deep(.cell-value-md) {
				font-size: var(--font-body-md);
				color: var(--light-list-normal-default-text);
			}

			.invoice-content {
				background-color: var(--light-fill-blank, white);
			}

			.expand-section {
				background-color: var(--light-fill-blank, white);

				.expand-header {
					display: flex;
					align-items: center;
					justify-content: center;
					height: 96rpx;
					background-color: transparent;
					padding: 16rpx, 0;

					.expand-text {
						color: #32364d;
						font-size: var(--font-body-sm);
					}

					.expand-icon {
						margin-left: 8rpx;
						display: flex;
						align-items: center;

						.arrow-icon {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}
			}

			.invoice-email {
				margin-top: 2rpx;
				background-color: var(--light-fill-blank, white);
				height: 128rpx;
				padding: var(--content-default-padding-lg) 0;
				border-radius: 0 0 var(--content-default-radius-md) var(--content-default-radius-md);
			}
		}
	}

	.footer-btns {
		display: flex;
		justify-content: space-between;
		padding: 24rpx 0 0;
		flex-shrink: 0;
		gap: var(--content-default-margin-md);

		.footer-btn {
			flex: 1;
		}
	}

	.orders-popup {
		padding: 40rpx 32rpx;

		.orders-popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 32rpx;

			.orders-popup-title {
				font-size: 32rpx;
				font-weight: 500;
				color: var(--light-text-title);
			}

			.orders-popup-close {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.orders-popup-content {
			max-height: 600rpx;
			overflow-y: auto;

			.order-item {
				background-color: var(--light-fill-light);
				border-radius: 16rpx;
				padding: 24rpx;
				margin-bottom: 24rpx;

				.order-date {
					font-size: 28rpx;
					color: var(--light-text-title);
					margin-bottom: 16rpx;
				}

				.order-info {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.order-type {
						font-size: 28rpx;
						color: var(--light-text-secondary);
					}

					.order-amount {
						font-size: 28rpx;
						font-weight: 500;
						color: var(--light-text-title);
					}
				}
			}
		}

		.orders-popup-footer {
			margin-top: 32rpx;
		}
	}
}
</style>
