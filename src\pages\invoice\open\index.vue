<template>
	<Layout title="缴费开票" @left-click="navigateBack" show-nav-bar backgroundImageType="default">
		<view class="page-container">
			<view class="step-wrapper">
				<UiStep :list="list" type="horizontal" :current="current"></UiStep>
			</view>

			<view class="content-wrapper">
				<!-- 根据当前步骤显示对应的组件 -->
				<OrderSelect
					v-if="current === 1"
					:orderData="invoiceData.orderSelect"
					@update:orderData="updateOrderData"
					@next-step="goToEdit"
				/>
				<InvoiceEdit
					v-else-if="current === 2"
					:invoiceData="invoiceData.invoiceEdit"
					@update:invoiceData="updateInvoiceEditData"
					@prev-step="goToOrder"
					@next-step="goToConfirm"
				/>
				<InvoiceConfirm
					v-else-if="current === 3"
					:confirmData="invoiceData.invoiceConfirm"
					:invoiceEditData="invoiceData.invoiceEdit"
					@update:confirmData="updateInvoiceConfirmData"
					@prev-step="goToEdit"
					@confirm="handleConfirm"
				/>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import UiStep from '@e-cloud/eslink-plus-uniapp/components/ui-step/ui-step.vue'
import Layout from '@/layout/index.vue'
import OrderSelect from './components/OrderSelect.vue'
import InvoiceEdit from './components/InvoiceEdit.vue'
import InvoiceConfirm from './components/InvoiceConfirm.vue'

defineOptions({
	name: 'InvoiceOpen',
})

// 步骤条配置
const list = ['订单选择', '开票填报', '开票确认']
const current = ref(1)

// 发票数据状态管理 - 所有组件的数据都保存在这里
const invoiceData = reactive({
	// OrderSelect 组件数据
	orderSelect: {
		activeOrderTab: 'available', // 当前选中的订单标签
		selectedFilter: 'all', // 当前选中的筛选选项
		selectedOrderIds: [], // 选中的订单ID列表
		selectedOrders: [], // 选中的完整订单数据
		availableOrderData: [
			{
				month: '2025年4月',
				items: [
					{
						date: '2025/04/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202504251',
					},
					{
						date: '2025/04/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202504252',
					},
					{
						date: '2025/04/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202504253',
					},
				],
			},
			{
				month: '2025年3月',
				items: [
					{
						date: '2025/03/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202503251',
					},
					{
						date: '2025/03/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202503252',
					},
					{
						date: '2025/03/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202503253',
					},
				],
			},
			{
				month: '2025年2月',
				items: [
					{
						date: '2025/02/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202502251',
					},
					{
						date: '2025/02/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202502252',
					},
					{
						date: '2025/02/25',
						type: '气费',
						amount: '102.40',
						selected: false,
						id: '202502253',
					},
				],
			},
		],
		otherOrderData: [
			{
				month: '2025年4月',
				items: [
					{
						date: '2025/04/20',
						type: '水费',
						amount: '85.60',
						id: '202504201',
					},
					{
						date: '2025/04/15',
						type: '电费',
						amount: '156.30',
						id: '202504151',
					},
				],
			},
			{
				month: '2025年3月',
				items: [
					{
						date: '2025/03/20',
						type: '水费',
						amount: '78.90',
						id: '*********',
					},
					{
						date: '2025/03/15',
						type: '电费',
						amount: '142.70',
						id: '*********',
					},
				],
			},
		],
		totalAmount: '0.00', // 总金额
	},

	// InvoiceEdit 组件数据
	invoiceEdit: {
		invoiceType: 'normal', // 发票类型（normal: 普通发票, special: 专用发票）
		titleType: '0', // 发票抬头类型 0：单位，1：个人
		formData: {
			title: '', // 抬头名称
			taxNumber: '', // 统一社会信用代码
			companyAddress: '', // 企业地址
			telephone: '', // 手机号码
			bankName: '', // 开户银行
			bankAccount: '', // 银行账号
			remark: '', // 备注
			email: '', // 电子邮箱
		},
		selectedOrders: [], // 选中的订单
		totalAmount: '0.00', // 总金额
	},

	// InvoiceConfirm 组件数据
	invoiceConfirm: {
		confirmInfo: {},
	},
})
// 导航函数
const navigateBack = () => {
	uni.navigateBack()
}

// 去订单选择页面
const goToOrder = () => {
	// 不需要清除选中的订单数据，保持状态
	current.value = 1
}

// 更新订单数据
const updateOrderData = newData => {
	invoiceData.orderSelect = newData
}

// 更新开票填报数据
const updateInvoiceEditData = newData => {
	invoiceData.invoiceEdit = newData
}

// 更新开票确认数据
const updateInvoiceConfirmData = newData => {
	invoiceData.invoiceConfirm = newData
}

// 去开票填报页面
const goToEdit = data => {
	// 保存选中的订单数据到 invoiceEdit 中
	if (data && data.selectedOrders) {
		invoiceData.invoiceEdit.selectedOrders = data.selectedOrders
		invoiceData.invoiceEdit.totalAmount = data.totalAmount
	}
	current.value = 2
}

// 去开票确认页面
const goToConfirm = () => {
	current.value = 3
}

// 处理确认开票
const handleConfirm = () => {
	// 处理确认开票逻辑
	uni.navigateTo({
		url: '/pages/invoice/success/index',
	})
}
</script>

<style scoped lang="scss">
.page-container {
	position: relative;
	display: flex;
	flex-direction: column;
	height: calc(100vh - var(--status-bar-height) - 88rpx); /* 减去状态栏和导航栏高度 */
	overflow: hidden;
}

.step-wrapper {
	position: relative;
	z-index: 1;
	padding: 24px 0;
	flex-shrink: 0;
}

.content-wrapper {
	flex: 1;
	position: relative;
	z-index: 1;
	box-sizing: border-box;
	overflow: hidden; /* 防止内容溢出 */
}
</style>
