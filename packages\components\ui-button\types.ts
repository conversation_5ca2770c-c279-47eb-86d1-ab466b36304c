import { SizeEnum } from '../../shared/enum'

export enum ButtonType {
	normal = 'normal',
	primary = 'primary',
	link = 'link',
	secondary = 'secondary',
	transparent = 'transparent',
	lighter = 'lighter',
}

export enum IconPosition {
	left = 'left',
	right = 'right',
}

export enum StyleName {
	light = 'light',
	dark = 'dark',
}

export interface ButtonProps {
	/**
	 * @description 按钮宽度
	 */
	width?: string
	/**
	 * @description 按钮风格
	 */
	styleName?: keyof typeof StyleName
	/**
	 * @description 按钮类型
	 */
	type: keyof typeof ButtonType
	/**
	 * @description 按钮尺寸
	 */
	size?: keyof typeof SizeEnum
	/**
	 * @description 按钮文字
	 */
	text?: string
	/**
	 * @description 图标字体名
	 */
	iconfontClass?: string
	/**
	 * @description 按钮图标
	 */
	icon?: string
	/**
	 * @description 按钮图标位置
	 */
	iconPosition?: keyof typeof IconPosition
	/**
	 * @description 是否禁用
	 */
	disabled?: boolean
	/**
	 * @description 开发能力
	 */
	openType?: string
	/**
	 * @description 打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效
	 */
	appParameter?: string
	/**
	 * @description 加载状态
	 */
	loading?: boolean
}
