<template>
	<view class="ui-dropdown" :class="[`ui-dropdown-size-${size}`]" @click="handlePopupClick">
		<view class="uni-dropdown-current">{{ currentLabel }}</view>
		<UiIcon name="arrow-down-s-fill"></UiIcon>
	</view>
	<UiPopup
		v-model="popupShow"
		ref="popupRef"
		position="bottom"
		:closeable="false"
		text-position="center"
		round
		mask-show
		is-mask-click
		:title="title"
	>
		<picker-view :value="currentValue" class="ui-dropdown-picker-view" @change="handlerChange">
			<picker-view-column>
				<view
					class="ui-dropdown-picker-view-item"
					:class="{ active: currentValue[0] === index }"
					v-for="(item, index) in options"
					:key="index"
				>
					{{ item.label }}
				</view>
			</picker-view-column>
		</picker-view>
		<view class="ui-dropdown-btn">
			<UiButton size="xlg" @click="handlerCancel">取消</UiButton>
			<UiButton size="xlg" type="secondary" @click="handlerSubmit">确认</UiButton>
		</view>
	</UiPopup>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineProps, watch } from 'vue'
import UiIcon from '../ui-icon/ui-icon.vue'
import UiPopup from '../ui-popup/ui-popup.vue'
import UiButton from '../ui-button/ui-button.vue'
import { type DropdownProps } from './types'

const popupRef = ref()
const popupShow = ref(false)
const currentValue = ref([])
const currentLabel = ref()
const props = withDefaults(defineProps<DropdownProps>(), {
	options: [],
	modelValue: '',
	title: '',
	size: 'md',
})
const emit = defineEmits(['update:modelValue', 'cancel', 'change'])
const handlerCancel = () => {
	emit('cancel')
	popupShow.value = false
}
const handlePopupClick = () => {
	popupShow.value = true
	init()
}
const handlerSubmit = () => {
	props.options.forEach((item: any, index: number) => {
		if (index === currentValue.value[0]) {
			currentLabel.value = item.label
			emit('update:modelValue', item.value)
			emit('change', item.value)
		}
	})
	popupShow.value = false
}
const handlerChange = e => {
	currentValue.value = e.detail.value
}
const init = () => {
	if (props.options.length) {
		props.options.forEach((item: any, index: number) => {
			if (item.value === props.modelValue) {
				currentValue.value = [index]
				currentLabel.value = item.label
			}
		})
	}
}
watch(
	() => props.options,
	(val: any[]) => {
		if (Array.isArray(val) && val.length) {
			init()
		}
	},
)
init()
</script>
<style lang="scss" scoped>
@use './ui-dropdown.scss';
</style>
