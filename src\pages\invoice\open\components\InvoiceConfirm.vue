<template>
	<view class="invoice-confirm">
		<!-- 内容区域（可滚动） -->
		<scroll-view class="content-scroll" scroll-y="true">
			<!-- 发票信息 -->
			<view class="confirm-content">
				<UiTitle title="发票信息" size="md" />
				<view class="confirm-info">
					<UiCell title="抬头类型" :value="titleTypeText" />
					<UiCell title="抬头名称" :value="invoiceEditData.formData.title || '-'" />
					<UiCell title="公司税号" :value="invoiceEditData.formData.taxNumber || '-'" />
					<UiCell title="公司地址" :value="invoiceEditData.formData.companyAddress || '-'" />
					<UiCell title="公司开户行" :value="invoiceEditData.formData.bankName || '-'" />
					<UiCell title="开户行账户" :value="invoiceEditData.formData.bankAccount || '-'" />
				</view>
			</view>

			<!-- 接收方式 -->
			<view class="confirm-content">
				<UiTitle title="接收方式" size="md" />
				<view class="confirm-info">
					<UiCell title="电子邮箱" :value="invoiceEditData.formData.email || '-'" />
					<UiCell title="备注" :value="invoiceEditData.formData.remark || '-'" />
				</view>
			</view>

			<!-- 底部空白区域，为底部按钮留出空间 -->
			<view class="footer-placeholder"></view>
		</scroll-view>

		<!-- 底部按钮（固定在底部） -->
		<view class="footer-buttons">
			<UiButton type="normal" size="lg" text="上一步" @click="prevStep"></UiButton>
			<UiButton type="primary" size="lg" text="确认开票" @click="confirmInvoice"></UiButton>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'

defineOptions({
	name: 'InvoiceConfirm',
})

// 接收从父组件传递的数据
const props = defineProps({
	confirmData: {
		type: Object,
		required: true,
	},
	invoiceEditData: {
		type: Object,
		required: true,
	},
})

const emit = defineEmits(['update:confirmData', 'prev-step', 'confirm'])

// 计算选中的订单数量
const selectedCount = computed(() => props.invoiceEditData.selectedOrders?.length || 0)

// 计算总金额
const totalAmount = computed(() => props.invoiceEditData.totalAmount || '0.00')

// 获取发票类型文本
const invoiceTypeText = computed(() => {
	return props.invoiceEditData.invoiceType === 'electronic' ? '增值税电子普通发票' : '增值税普通发票'
})

// 获取发票抬头文本
const titleTypeText = computed(() => {
	return props.invoiceEditData.titleType === '1' ? '个人' : '企业'
})

// 上一步
const prevStep = () => {
	emit('prev-step')
}

// 确认开票
const confirmInvoice = () => {
	emit('confirm')
}
</script>

<style scoped lang="scss">
.invoice-confirm {
	display: flex;
	flex-direction: column;
	height: 100vh;
	position: relative;

	.content-scroll {
		flex: 1;
		overflow: hidden;
	}

	.confirm-content {
		background-color: #ffffff;
		border-radius: var(--content-default-radius-md);
		padding: var(--content-default-padding-sm) 0;
		margin-bottom: 24rpx;

		:deep(.ui-title-title) {
			color: var(--light-text-title);
		}

		:deep(.cell-title) {
			font-size: var(--font-body-md);
			color: var(--light-list-normal-default-text-light);
		}

		:deep(.cell-value-md) {
			font-size: var(--font-body-md);
			color: var(--light-list-normal-default-text);
		}
	}

	.footer-placeholder {
		height: 160rpx; /* 为底部按钮留出空间 */
	}

	.footer-buttons {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: var(--content-default-radius-md);
		background-color: var(--light-bg-base);
		z-index: 99;

		:deep(.ui-button) {
			width: 48%;
		}
	}
}
</style>
