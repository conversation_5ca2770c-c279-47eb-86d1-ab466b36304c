<template>
	<view class="load-more-wrap">
		<image v-if="status == 'loading'" ref="loadIconRef" class="load-more-icon" :src="loadIcon"></image>
		<view class="load-more-text">{{ getLoadingText() }}</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import loadMoreIcon from '../../assets/images/components/ui-load-more/load-more-icon.png'

defineOptions({
	name: 'UiLoadMore',
})

const props = defineProps({
	status: {
		type: String, // more/loading/noMore
		default: '',
	},
	loadIcon: {
		type: String,
		default: loadMoreIcon,
	},
	moreText: {
		type: String,
		default: '上拉加载更多',
	},
	loadingText: {
		type: String,
		default: '加载中...',
	},
	noMoreText: {
		type: String,
		default: '没有更多数据了',
	},
})

const loadIconRef = ref()

const emits = defineEmits(['change'])

const getLoadingText = () => { 
	if (props.status == 'loading') {
		return props.loadingText;
	} else if (props.status == 'noMore') {
		return props.noMoreText;
	} else {
		return props.moreText;
	}
}

</script>
<style scoped lang="scss">
@use './ui-load-more.scss';
</style>
