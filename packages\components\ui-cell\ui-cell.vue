<template>
	<view class="setting-cell-wrap" :class="['cell-size-' + (size || 'md'), noPadding ? 'noPadding' : '', background ? 'background' : '', transparent ? 'transparent' : '' ]" @click="cellClick">
		<view class="cell-left">
			<view class="cell-icon-wrap" :class="['cell-icon-' + (size || 'md')]">
				<slot name="prefix">
					<i v-if="icon" :class="'cell-icon iconfont ' + icon"></i>
				</slot>
			</view>
			<view class="cell-title-wrap" :class="['cell-size-' + (size || 'md')]">
				<view :class="['cell-title', 'cell-title-' + (size || 'md') ]">{{ title }}</view>
			</view>
			<view class="sub-title-wrap" :class="['cell-icon-' + (size || 'md')]">
				<slot name="subTitle">
					<view :class="['cell-subTitle-' + (size || 'md') ]">{{ subTitle }}</view>
				</slot>
			</view>
			
		</view>
		<view class="cell-right">
			<slot name="valuePrefix">
				<view class="cell-value-prefix" v-if="valuePrefix">{{ valuePrefix }}</view>
			</slot>
			<slot name="value">
				<view v-if="value">
					
					<UiMoney
						v-if="valueType == 'money'"
						size="sm"
					>
						{{ value }}
					</UiMoney>
					<UiNumber
						v-else-if="valueType == 'number'"
						weight="normal"
						:value="value"
						unit=""
					></UiNumber>
					<view :class="['cell-value-' + (size || 'md') ]" v-else>{{ value }}</view>
				</view>
			</slot>
			<slot name="valueSuffix">
				<view class="cell-value-suffix" v-if="valueSuffix">{{ valueSuffix }}</view>
			</slot>
			<UiTag v-if="tagValue" :type="tagType" size="sm" :text="tagValue"></UiTag>
			<i v-if="arrow" class="iconfont icon-sysicon"></i>
		</view>
	</view>
	<view class="line" v-if="underline"></view>
</template>

<script setup lang="ts">
import UiTag from '../ui-tag/ui-tag.vue'
import UiNumber from '../ui-number/ui-number.vue'
import UiMoney from '../ui-money/ui-money.vue'
import type { CellProps } from './types'

defineOptions({
	name: 'SettingCell',
})

const props = withDefaults(defineProps<CellProps>(), {
	id: 0,
	icon: '',
	title: '',
	subTitle: '',
	arrow: false,
	size: 'md',
	value: '',
	valuePrefix: '',
	valueType: 'text',
	valueSuffix: '',
	noPadding: false,
	underline: false,
	tagValue: '',
	tagType: 'default',
	background: false,
	transparent: false,
})

const emits = defineEmits(['cellClick'])
// cell 点击
const cellClick = () => {
	emits('cellClick', props)
}
</script>

<style lang="scss" scoped>
@use './ui-cell.scss';
</style>
