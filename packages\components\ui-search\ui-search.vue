<template>
	<div class="ui-search">
		<div class="ui-search-content">
			<div class="ui-search-icon">
				<UiIcon name="search-2-line"></UiIcon>
			</div>
			<input @input="handlerChange" :placeholder="placeholder" v-model="searchValue" class="ui-search-input" />
			<div class="ui-search-close-icon" v-if="searchValue">
				<UiIcon name="close-line" @click="handlerClear"></UiIcon>
			</div>
			<UiButton type="link" @click="handlerClick" class="ui-search-button" v-if="searchValue">搜索</UiButton>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import UiIcon from '../ui-icon/ui-icon.vue'
import UiButton from '../ui-button/ui-button.vue'
import { type SearchProps } from './types'

const props = withDefaults(defineProps<SearchProps>(), {
	placeholder: '请输入内容',
	modelValue: '',
})

const searchValue = ref(props.modelValue)
const emits = defineEmits(['search', 'clear', 'update:modelValue', 'change'])
const handlerClear = () => {
	searchValue.value = ''
	emits('update:modelValue', searchValue.value)
	emits('clear')
}

const handlerChange = () => {
	emits('update:modelValue', searchValue.value)
	emits('change', searchValue.value)
}
const handlerClick = () => {
	emits('update:modelValue', searchValue.value)
	emits('search', searchValue.value)
}
watch(
	() => props.modelValue,
	(value: string) => {
		searchValue.value = value
	}
)
</script>
<style lang="scss" scoped>
@use './ui-search.scss';
</style>
