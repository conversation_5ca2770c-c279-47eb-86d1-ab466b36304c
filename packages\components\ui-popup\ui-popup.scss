.ui-popup {
	&.bottom {
		width: 100vw;
		padding-bottom: env(safe-area-inset-bottom);
		border-top-left-radius: var(--content-default-radius-xlg);
		border-top-right-radius: var(--content-default-radius-xlg);
	}

	&.center {
		border-radius: var(--content-default-radius-xlg);
	}

	&.top {
		width: 100vw;
		border-bottom-left-radius: var(--content-default-radius-xlg);
		border-bottom-right-radius: var(--content-default-radius-xlg);
	}

	&.right {
		height: 100vh;
		padding-bottom: env(safe-area-inset-bottom);
		border-top-left-radius: var(--content-default-radius-xlg);
		border-bottom-left-radius: var(--content-default-radius-xlg);
	}

	&.left {
		height: 100vh;
		padding-bottom: env(safe-area-inset-bottom);
		border-top-right-radius: var(--content-default-radius-xlg);
		border-bottom-right-radius: var(--content-default-radius-xlg);
	}
}
.ui-popup-blank {
	background: var(--light-fill-blank);
}

.ui-popup-lighter {
	background: var(--light-fill-lighter);
}

.ui-popup-header,
.ui-popup-footer {
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
}

.ui-popup-header {
	min-height: 128rpx;
	&.text-center {
		justify-content: center;
	}
	&.icon-top-left {
		flex-direction: row-reverse;
	}
}

.ui-popup-title {
	font-weight: 600;
	font-size: var(--font-title-lg);
	color: var(--light-text-title, #171928);
}

.ui-popup-footer {
	min-height: 64rpx;
	&.top-right,
	&.bottom-right {
		flex-direction: row-reverse;
	}
}

.ui-popup-close {
	font-size: 48rpx;
}
