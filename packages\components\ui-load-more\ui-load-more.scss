.load-more-wrap {
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 30rpx;
	.load-more-icon {
		width: 36rpx;
		height: 36rpx;
		animation: spin 2s linear infinite;  
	}
	.load-more-text {
		margin-top: 14rpx;
		font-size: var(--font-body-md);
		color: var(--light-text-light)
	}
}

@keyframes spin {  
	from {  
	  transform: rotate(0deg);  
	}  
	to {  
	  transform: rotate(360deg);  
	}  
  } 
