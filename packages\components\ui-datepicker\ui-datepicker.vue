<template>
	<view class="ui-datepicker" @click="handlePopupClick">
		<view class="uni-datepicker-current">{{ currentLabel }}</view>
		<UiIcon name="arrow-down-s-fill"></UiIcon>
	</view>
	<UiPopup
		v-model="popupShow"
		ref="popupRef"
		position="bottom"
		:closeable="false"
		text-position="center"
		round
		mask-show
		is-mask-click
		:title="title"
	>
		<picker-view :value="currentValue" class="ui-datepicker-picker-view" @change="handlerChange">
			<picker-view-column>
				<view
					class="ui-datepicker-picker-view-item"
					:class="{ active: currentValue[0] === index }"
					v-for="(item, index) in options1"
					:key="index"
				>
					{{ item.label }}
				</view>
			</picker-view-column>
			<picker-view-column v-if="type === 'day' || type === 'month'">
				<view
					class="ui-datepicker-picker-view-item"
					:class="{ active: currentValue[1] === index }"
					v-for="(item, index) in options2"
					:key="index"
				>
					{{ item.label }}
				</view>
			</picker-view-column>
			<picker-view-column v-if="type === 'day'">
				<view
					class="ui-datepicker-picker-view-item"
					:class="{ active: currentValue[2] === index }"
					v-for="(item, index) in options3"
					:key="index"
				>
					{{ item.label }}
				</view>
			</picker-view-column>
		</picker-view>
		<view class="ui-datepicker-btn">
			<UiButton size="xlg" @click="handlerCancel">取消</UiButton>
			<UiButton size="xlg" type="secondary" @click="handlerSubmit">确认</UiButton>
		</view>
	</UiPopup>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineProps } from 'vue'
import UiIcon from '../ui-icon/ui-icon.vue'
import UiPopup from '../ui-popup/ui-popup.vue'
import UiButton from '../ui-button/ui-button.vue'
import { type DatePickerProps } from './types'
import dayjs from '../../lib/dayjs/esm'

const props = withDefaults(defineProps<DatePickerProps>(), {
	options: [],
	modelValue: '',
	title: '',
	type: 'day',
})
const popupRef = ref()
const popupShow = ref(false)
const currentValue = ref([])
const currentLabel = ref()
const options1 = ref([])
const options2 = ref([])
const options3 = ref([])
const emit = defineEmits(['update:modelValue', 'cancel', 'change'])
const initYear = () => {
	options1.value = Array.from(new Array(100)).map((_, i) => {
		return {
			label: dayjs().subtract(i, 'year').format('YYYY'),
			value: dayjs().subtract(i, 'year').format('YYYY'),
		}
	})
}
const initMonth = () => {
	options2.value = Array.from(new Array(12)).map((_, i) => {
		return {
			label: dayjs().month(i).format('MM'),
			value: dayjs().month(i).format('MM'),
		}
	})
}
const initDay = () => {
	options3.value = Array.from(new Array(31)).map((_, i) => {
		return {
			label: dayjs()
				.date(i + 1)
				.format('DD'),
			value: dayjs()
				.date(i + 1)
				.format('DD'),
		}
	})
}
const handlerCancel = () => {
	emit('cancel')
	popupShow.value = false
}
const handlePopupClick = () => {
	popupShow.value = true
	init()
}
const init = () => {
	let data
	if (props.modelValue) {
		data = dayjs(props.modelValue)
	} else {
		data = dayjs()
	}
	if (options1.value.length) {
		options1.value.forEach((item: any, index: number) => {
			if (item.value === data.format('YYYY')) {
				currentValue.value = [index]
				currentLabel.value = item.label
				emit('update:modelValue', data.format('YYYY'))
			}
		})
	}
	if (options2.value.length && props.type === 'month') {
		options2.value.forEach((item: any, index: number) => {
			if (item.value === data.format('MM')) {
				currentValue.value.push(index)
				currentLabel.value = currentLabel.value + '-' + item.label
				emit('update:modelValue', data.format('YYYY-MM'))
			}
		})
	}
}
const handlerSubmit = () => {
	let data = options1.value[currentValue.value[0]].label
	if (props.type === 'month' || props.type === 'day') {
		data = data + '-' + options2.value[currentValue.value[1]].label
	}
	if (props.type === 'day') {
		data = data + '-' + options3.value[currentValue.value[2]].label
	}
	currentLabel.value = data
	emit('update:modelValue', data)
	emit('change', data)
	popupShow.value = false
}
const handlerChange = (e: any) => {
	currentValue.value = e.detail.value
}
initYear()
initMonth()
initDay()
init()
</script>
<style lang="scss" scoped>
@use './ui-datepicker.scss';
</style>
