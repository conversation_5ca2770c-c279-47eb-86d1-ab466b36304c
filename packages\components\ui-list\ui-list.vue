<template>
	<view class="list-view-wrap">
		<scroll-view
			class="scroll-view"
			scroll-y="true"
			lower-threshold="10"
			refresher-default-style="none"
			:show-scrollbar="false"
			refresher-background="transparent"
			@scrolltolower="handleScrollToLower"
			@refresherrefresh="handleRefresh"
			refresher-enabled
			:refresher-triggered="loading"
		>	
			<UiLoadMore v-if="loading" status="loading" />
			<slot :dataList="dataList" :getData="hasGetData"></slot>
			<UiLoadMore v-if="dataList.length" :status="loadMoreStatus" />
		</scroll-view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UiLoadMore from '../ui-load-more/ui-load-more.vue'

defineOptions({
	name: 'ListView',
})

const props = defineProps({
	api: {
		type: Function,
		default: () => {},
	},
	params: {
		type: Object,
		default: () => {},
	},
	dataPath: {
		type: String,
		default: '',
	},
	pageSize: {
		type: Number,
		default: 10,
	},
})

const emits = defineEmits(['updateList', 'refresh', 'loadMore'])

const pageConfig = {
	pageNum: 1,
	pageSize: props.pageSize,
}
const dataList = ref([] as any)
const loading = ref(false)
const loadMoreStatus = ref('more')
const hasGetData = ref(false)

// 查询列表数据
const getDataList = () => {
	let params = {
		...props.params,
		...pageConfig,
	}
	props
		.api(params)
		.then((res: any) => {
			if (res) {
				let list = []
				list = res[props.dataPath || 'list']
				if (Array.isArray(list)) {
					dataList.value = [...dataList.value, ...list]
				}
				if (dataList.value.length >= res.total) {
					loadMoreStatus.value = 'noMore'
				} else {
					loadMoreStatus.value = 'more'
				}
				emits('updateList', dataList.value)
			}
		})
		.catch(() => {
			loadMoreStatus.value = 'noMore'
		})
		.finally(() => {
			hasGetData.value = true
			loading.value = false
		})
}

// 下拉刷新
const handleRefresh = () => { 
	loading.value = true
	refresh()
}
// 刷新页面
const refresh = () => {
	hasGetData.value = false
	setTimeout(() => {
		dataList.value = []
		pageConfig.pageNum = 1
		pageConfig.pageSize = props.pageSize
		loadMoreStatus.value = 'more'
		getDataList()
		emits('refresh')
	}, 0)
}

// 上拉加载
const handleScrollToLower = () => {
	if (loadMoreStatus.value == 'more') {
		loadMoreStatus.value = 'loading'
		pageConfig.pageNum++
		getDataList()
	}
	emits('loadMore')
}

defineExpose({
	refresh,
})
</script>
<style lang="scss" scoped>
@use './ui-list.scss';
</style>
