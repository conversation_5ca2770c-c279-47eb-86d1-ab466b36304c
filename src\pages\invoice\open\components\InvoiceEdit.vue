<template>
	<view class="invoice-edit">
		<!-- 发票总额栏 -->
		<view class="invoice-amount">
			<UiCell
				title="发票总额"
				:value="`${totalAmount || 0.0}`"
				:tagValue="`${selectedCount}笔订单`"
				valueType="money"
			></UiCell>
		</view>

		<!-- 内容区域（可滚动） -->
		<scroll-view class="content-scroll" scroll-y="true" :style="{ height: scrollViewHeight }">
			<!-- 发票类型选择 -->
			<UiCardTabs
				v-model="invoiceData.invoiceType"
				:options="invoiceTypeOptions"
				@change="handleInvoiceTypeChange"
			>
				<!-- 发票填写表单 -->
				<view class="invoice-form">
					<!-- 抬头类型 -->
					<view class="form-item title-type">
						<view class="form-row">
							<view class="item-label">
								抬头类型
								<text class="required-mark">*</text>
							</view>
							<view class="form-content">
								<view class="radio-group">
									<view class="radio-item">
										<UiRadio
											:checked="invoiceData.titleType === '0'"
											name="0"
											@change="() => updateTitleType('0')"
										></UiRadio>
										<text class="radio-label">企业单位</text>
									</view>
									<view class="radio-item">
										<UiRadio
											:checked="invoiceData.titleType === '1'"
											name="1"
											@change="() => updateTitleType('1')"
										></UiRadio>
										<text class="radio-label">个人/非企业</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 抬头名称 -->
					<view class="form-item">
						<view class="form-row">
							<view class="item-label">
								抬头名称
								<text class="required-mark">*</text>
							</view>
							<view class="form-content">
								<view class="input-wrapper">
									<ui-input
										type="text"
										placeholder="必填"
										:bottomLine="false"
										v-model="invoiceData.formData.title"
										@update:modelValue="val => updateFormData('title', val)"
									/>
									<UiButton class="select-btn" size="md" type="normal" @click="selectCompany">
										选择
									</UiButton>
								</view>
							</view>
						</view>
					</view>

					<!-- 公司税号 -->
					<view class="form-item">
						<view class="form-row">
							<view class="item-label">
								公司税号
								<text class="required-mark">*</text>
							</view>
							<view class="form-content">
								<view class="input-wrapper">
									<ui-input
										type="text"
										placeholder="必填"
										:bottomLine="false"
										v-model="invoiceData.formData.taxNumber"
										@update:modelValue="val => updateFormData('taxNumber', val)"
									/>
								</view>
							</view>
						</view>
						<view class="form-divider"></view>
					</view>

					<!-- 展开收起选填内容 -->
					<view class="expand-section">
						<view class="expand-content" v-if="isExpanded">
							<!-- 公司地址 -->
							<view class="form-item">
								<view class="form-row">
									<view class="item-label">公司地址</view>
									<view class="form-content">
										<view class="input-wrapper">
											<ui-input
												type="text"
												placeholder="请输入"
												:bottomLine="false"
												v-model="invoiceData.formData.companyAddress"
												@update:modelValue="val => updateFormData('companyAddress', val)"
											/>
										</view>
									</view>
								</view>
								<view class="form-divider"></view>
							</view>

							<!-- 公司电话 -->
							<view class="form-item">
								<view class="form-row">
									<view class="item-label">公司电话</view>
									<view class="form-content">
										<view class="input-wrapper">
											<ui-input
												type="text"
												placeholder="请输入"
												:bottomLine="false"
												v-model="invoiceData.formData.telephone"
												@update:modelValue="val => updateFormData('telephone', val)"
											/>
										</view>
									</view>
								</view>
								<view class="form-divider"></view>
							</view>

							<!-- 公司开户行 -->
							<view class="form-item">
								<view class="form-row">
									<view class="item-label">公司开户行</view>
									<view class="form-content">
										<view class="input-wrapper">
											<ui-input
												type="text"
												placeholder="请输入"
												:bottomLine="false"
												v-model="invoiceData.formData.bankName"
												@update:modelValue="val => updateFormData('bankName', val)"
											/>
										</view>
									</view>
								</view>
								<view class="form-divider"></view>
							</view>

							<!-- 开户行账户 -->
							<view class="form-item">
								<view class="form-row">
									<view class="item-label">开户行账户</view>
									<view class="form-content">
										<view class="input-wrapper">
											<ui-input
												type="text"
												placeholder="请输入"
												:bottomLine="false"
												v-model="invoiceData.formData.bankAccount"
												@update:modelValue="val => updateFormData('bankAccount', val)"
											/>
										</view>
									</view>
								</view>
								<view class="form-divider"></view>
							</view>

							<!-- 备注 -->
							<view class="remark-item">
								<view class="remark-label">备注</view>
								<view class="remark-content">
									<view class="input-wrapper">
										<ui-input
											type="textarea"
											placeholder="请输入"
											:bottomLine="false"
											v-model="invoiceData.formData.remark"
											:maxlength="50"
											:rows="3"
											@update:modelValue="val => updateFormData('remark', val)"
										/>
									</view>
								</view>
							</view>
						</view>
						<view class="expand-header" @click="toggleExpand">
							<text class="expand-text">{{ isExpanded ? '收起信息（选填）' : '展开信息（选填）' }}</text>
							<view class="expand-icon">
								<image
									:src="
										isExpanded
											? '/static/images/invoice/arrow-up-s-fill.png'
											: '/static/images/invoice/arrow-down-s-fill.png'
									"
									class="arrow-icon"
								/>
							</view>
						</view>
					</view>
				</view>
			</UiCardTabs>
			<!-- 接收方式 -->
			<view class="receive-method">
				<UiTitle title="接收方式" size="sm"></UiTitle>
			</view>

			<!-- 电子邮箱 -->
			<view class="email-item">
				<view class="email-label">电子邮箱</view>
				<view class="email-content">
					<ui-input
						type="text"
						placeholder="请输入"
						:bottomLine="false"
						v-model="invoiceData.formData.email"
						:clearable="true"
						@update:modelValue="val => updateFormData('email', val)"
					/>
				</view>
			</view>

			<!-- 底部空白区域，为底部按钮留出空间 -->
			<view class="footer-placeholder"></view>
		</scroll-view>

		<!-- 底部按钮（固定在底部） -->
		<view class="footer-buttons">
			<UiButton type="normal" size="lg" text="上一步" @click="prevStep"></UiButton>
			<UiButton type="secondary" size="lg" text="下一步" @click="nextStep"></UiButton>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiCardTabs from '@e-cloud/eslink-plus-uniapp/components/ui-card-tabs/ui-card-tabs.vue'
import UiRadio from '@e-cloud/eslink-plus-uniapp/components/ui-radio/ui-radio.vue'
import UiInput from '@e-cloud/eslink-plus-uniapp/components/ui-input/ui-input.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import { chooseInvoiceTitle } from '@/core/util'

defineOptions({
	name: 'InvoiceEdit',
})

// 接收从父组件传递的数据
const props = defineProps({
	invoiceData: {
		type: Object,
		required: true,
	},
})

const emit = defineEmits(['update:invoiceData', 'prev-step', 'next-step'])

// 计算选中的订单数量
const selectedCount = computed(() => props.invoiceData.selectedOrders?.length || 0)

// 计算总金额
const totalAmount = computed(() => props.invoiceData.totalAmount || '0.00')

// 发票类型选项
const invoiceTypeOptions = [
	{ value: 'normal', label: '普通发票' },
	{ value: 'special', label: '专用发票' },
]

// 处理发票类型变更
const handleInvoiceTypeChange = (type: string) => {
	updateInvoiceType(type)
}

// 选择公司
const selectCompany = () => {
	chooseInvoiceTitle()
		.then((res: any) => {
			if (res) {
				// 将选择的发票抬头信息填入表单
				const newFormData = { ...props.invoiceData.formData }
				newFormData.title = res.title
				newFormData.taxNumber = res.taxNumber
				newFormData.address = res.companyAddress
				newFormData.phone = res.telephone
				newFormData.bank = res.bankName
				newFormData.account = res.bankAccount

				emit('update:invoiceData', {
					...props.invoiceData,
					formData: newFormData,
				})
			}
		})
		.catch(err => {
			console.error('选择发票抬头失败', err)
		})
}

// 控制展开收起状态
const isExpanded = ref(false)
// 控制接收方式展开收起状态
const isReceiveExpanded = ref(false)

// 计算滚动视图高度
const scrollViewHeight = computed(() => {
	// 底部按钮高度 + 顶部发票总额栏高度 + 安全距离
	const bottomHeight = 120
	const topHeight = 100
	const safeDistance = 20

	// 当内容展开时，减少固定高度限制，允许更多滚动空间
	if (isExpanded.value || isReceiveExpanded.value) {
		return `calc(100vh - ${bottomHeight + topHeight}px)` // 减少安全距离
	}

	return `calc(100vh - ${bottomHeight + topHeight + safeDistance}px)`
})

// 切换展开收起状态
const toggleExpand = () => {
	isExpanded.value = !isExpanded.value
}

// 更新发票类型
const updateInvoiceType = (type: string) => {
	emit('update:invoiceData', {
		...props.invoiceData,
		invoiceType: type,
	})
}

// 更新发票抬头类型
const updateTitleType = (type: string) => {
	emit('update:invoiceData', {
		...props.invoiceData,
		titleType: type,
	})
}

// 更新表单数据
const updateFormData = (field: string | number, value: any) => {
	// 确保 formData 存在
	const formData = props.invoiceData.formData || {}
	const newFormData = { ...formData }
	newFormData[field] = value

	// 打印调试信息
	console.log(`更新字段: ${field}, 值: ${value}`)
	console.log('当前表单数据:', JSON.stringify(newFormData))

	emit('update:invoiceData', {
		...props.invoiceData,
		formData: newFormData,
	})
}

// 上一步
const prevStep = () => {
	emit('prev-step')
}

// 下一步
const nextStep = () => {
	// 确保 formData 存在
	const formData = props.invoiceData.formData || {}

	// 打印当前表单数据，用于调试
	console.log('当前表单数据:', JSON.stringify(formData))
	console.log('抬头名称:', formData.title)

	// 表单验证
	if (!formData.title) {
		console.log('抬头名称为空，显示提示')
		uni.showToast({
			title: '抬头名称不能为空',
			icon: 'none',
		})
		return
	}

	if (!formData.taxNumber) {
		uni.showToast({
			title: '公司税号不能为空',
			icon: 'none',
		})
		return
	}

	emit('next-step')
}
</script>

<style scoped lang="scss">
.invoice-edit {
	display: flex;
	flex-direction: column;
	min-height: 100vh; /* 改为最小高度，允许内容撑开 */
	position: relative;
	/* 移除 overflow: hidden，允许页面滚动 */

	.invoice-amount {
		background-color: #ffffff;
		border-radius: 20rpx;
		overflow: hidden;
		flex-shrink: 0;
		margin-bottom: 24rpx;

		:deep(.ui-cell) {
			padding: 32rpx;

			.ui-cell__title {
				font-size: 28rpx;
				color: #5f677d;
			}

			.ui-cell__value {
				font-size: 36rpx;
				color: #ff6b2c;
				font-weight: 500;
				font-family: TCloudNumber, sans-serif;
			}
		}

		.order-count {
			font-size: 24rpx;
			color: #5f677d;
			margin-top: 8rpx;
		}
	}

	.content-scroll {
		/* 使用动态高度而不是flex */
		width: 100%;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch; /* iOS滚动优化 */
	}

	.invoice-form {
		background-color: #ffffff;
		padding: 0 16rpx;
		border-radius: 20rpx;

		.required-mark {
			color: #ff0000;
			font-size: 28rpx;
			margin-left: 4rpx;
		}
		.remark-item {
			padding: 0 16rpx;
			display: flex;
			flex-direction: column;
			border-bottom: 2rpx solid #ebedf6;

			.remark-label {
				display: flex;
				align-items: center; // 垂直居中
				font-size: var(--font-body-md);
				min-height: 64rpx;
			}
		}
		.form-item {
			padding: 0 16rpx;
			min-height: 88rpx;
			display: flex;
			align-items: center;
			border-bottom: 2rpx solid #ebedf6;

			.form-row {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.item-label {
					width: 150rpx;
					margin-right: 32rpx;
					font-size: 28rpx;
					color: #32364d;
				}

				.form-content {
					flex: 1;
				}
			}

			.required-tip {
				font-size: 24rpx;
				color: #ff4d4f;
				margin-top: 8rpx;
			}

			.input-wrapper {
				display: flex;
				align-items: center;
				position: relative;

				input,
				textarea {
					flex: 1;
					height: 80rpx;
					font-size: 28rpx;
					color: #333333;
					background-color: transparent;
					padding: 0;
					border: none;
				}

				textarea {
					height: 160rpx;
					padding: 0;
				}
			}

			&.title-type {
				.radio-group {
					display: flex;

					.radio-item {
						display: flex;
						align-items: center;
						margin-right: 24rpx;

						&:last-child {
							margin-right: 0;
						}

						.radio-label {
							font-size: 28rpx;
							color: #333333;
							margin-left: 32rpx;
						}
					}
				}
			}
		}

		.expand-section {
			.expand-header {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80rpx;
				background-color: transparent;

				.expand-text {
					color: #32364d;
					font-size: var(--font-body-sm);
				}

				.expand-icon {
					margin-left: 8rpx;
					display: flex;
					align-items: center;

					.arrow-icon {
						width: 32rpx;
						height: 32rpx;
					}
				}
			}
		}
	}

	.footer-placeholder {
		height: 20rpx; /* 增加高度，为展开内容和底部按钮留出更多空间 */
	}

	.receive-method {
		min-height: 64rpx;
		display: flex;
		align-items: center;
		padding: 0, 32rpx;
		margin-top: 20rpx;
		font-size: var(--font-body-md);
		color: #5f677d;
	}

	.email-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: var(--content-default-padding-lg);
		min-height: 152rpx;
		justify-content: space-between;
		background-color: #ffffff;
		border-radius: var(--content-default-radius-md);

		.email-label {
			width: 150rpx;
			margin-right: 32rpx;
			font-size: 28rpx;
			color: #32364d;
		}

		.email-content {
			flex: 1;
		}
	}

	.footer-buttons {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: var(--content-default-radius-md);
		background-color: var(--light-bg-base);
		z-index: 99;

		:deep(.ui-button) {
			width: 48%;
		}
	}
}
</style>
