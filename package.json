{"name": "@e-cloud/eslink-plus-uniapp", "version": "0.0.27-alpha", "main": "dist/eslink-plus-uniapp.umd.js", "exports": {"./dist/style.css": "./dist/style.css", "./styles/index.scss": "./packages/styles/index.scss", "./components/ui-button/ui-button.vue": "./packages/components/ui-button/ui-button.vue", "./components/ui-money/ui-money.vue": "./packages/components/ui-money/ui-money.vue", "./components/ui-icon/ui-icon.vue": "./packages/components/ui-icon/ui-icon.vue", "./components/ui-search/ui-search.vue": "./packages/components/ui-search/ui-search.vue", "./components/ui-sidebar/ui-sidebar.vue": "./packages/components/ui-sidebar/ui-sidebar.vue", "./components/ui-card-tabs/ui-card-tabs.vue": "./packages/components/ui-card-tabs/ui-card-tabs.vue", "./components/ui-dropdown/ui-dropdown.vue": "./packages/components/ui-dropdown/ui-dropdown.vue", "./components/ui-message/ui-message.vue": "./packages/components/ui-message/ui-message.vue", "./components/ui-datepicker/ui-datepicker.vue": "./packages/components/ui-datepicker/ui-datepicker.vue"}, "style": "dist/style.css", "scripts": {"route": "node ./bin/route.js", "commit": "git add . && git cz", "dev": "npm run dev:h5", "dev:h5": "cd example && npm run route && uni", "dev:mp-alipay": "cd example && npm run route && uni -p mp-alipay", "dev:mp-weixin": "cd example && npm run route && uni -p mp-weixin", "build": "vite build", "build:h5": "cd example && npm run route && uni build", "build:mp-alipay": "cd example && npm run route && uni build -p mp-alipay", "build:mp-weixin": "cd example && npm run route && uni build -p mp-weixin", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-****************", "@dcloudio/uni-app-harmony": "3.0.0-****************", "@dcloudio/uni-app-plus": "3.0.0-****************", "@dcloudio/uni-components": "3.0.0-****************", "@dcloudio/uni-h5": "3.0.0-****************", "@dcloudio/uni-mp-alipay": "3.0.0-****************", "@dcloudio/uni-mp-baidu": "3.0.0-****************", "@dcloudio/uni-mp-harmony": "3.0.0-****************", "@dcloudio/uni-mp-jd": "3.0.0-****************", "@dcloudio/uni-mp-kuaishou": "3.0.0-****************", "@dcloudio/uni-mp-lark": "3.0.0-****************", "@dcloudio/uni-mp-qq": "3.0.0-****************", "@dcloudio/uni-mp-toutiao": "3.0.0-****************", "@dcloudio/uni-mp-weixin": "3.0.0-****************", "@dcloudio/uni-mp-xhs": "3.0.0-****************", "@dcloudio/uni-quickapp-webview": "3.0.0-****************", "dayjs": "^1.11.13", "vue": "3.4.21", "vue-i18n": "9.14.4", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@commitlint/config-conventional": "^12.1.0", "@dcloudio/types": "3.4.15", "@dcloudio/uni-automator": "3.0.0-****************", "@dcloudio/uni-cli-shared": "3.0.0-****************", "@dcloudio/uni-stacktracey": "3.0.0-****************", "@dcloudio/vite-plugin-uni": "3.0.0-****************", "@e-cloud/es-commitlint": "^0.0.3", "@types/crypto-js": "^4.2.2", "@types/node": "^20.17.50", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-legacy": "^5.4.3", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-standard": "^8.0.1", "@vue/eslint-config-typescript": "^11.0.3", "@vue/runtime-core": "3.5.14", "@vue/tsconfig": "^0.1.3", "crypto-js": "^4.2.0", "eslint": "^8.57.0", "eslint-config-prettier": "7.1.0", "eslint-define-config": "^1.5.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.3.0", "husky": "^6.0.0", "lint-staged": "^12.5.0", "postcss-rpx-transform": "^1.0.1", "prettier": "^3.2.5", "rollup-plugin-copy": "^3.5.0", "sass": "^1.89.0", "sass-loader": "^16.0.5", "stylelint": "^16.13.2", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended": "^15.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^37.0.0", "stylelint-config-standard-scss": "^14.0.0", "typescript": "^4.9.4", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "5.2.8", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-dts": "^4.5.4", "vue-tsc": "^1.0.24"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{scss,less,vue}": "stylelint --config '.stylelintrc.js' --fix", "*.{vue,ts,js}": "eslint --fix", "*.{js,jsx,ts,tsx,html,css,vue,less,scss}": "prettier --plugin-search-dir ./node_modules --write"}, "config": {"commitizen": {"path": "@e-cloud/es-commitlint"}}, "files": ["packages", "package.json", "README.md", "dist", "types"]}