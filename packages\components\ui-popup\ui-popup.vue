<template>
	<UiPopupContainer
		ref="popupContainerRef"
		:position="position"
		:z-index="zIndex"
		:mask-show="maskShow"
		:is-mask-click="isMaskClick"
		@change="handleChange"
	>
		<view
			class="ui-popup"
			:style="uiPopupStyle"
			:class="[position, round ? 'ui-popup-round' : '', `ui-popup-${backgroundType}`]"
		>
			<view class="ui-popup-header" :class="[`icon-${props.iconPosition}`, `text-${props.textPosition}`]">
				<view v-if="!!title" class="ui-popup-title">{{ title }}</view>
				<i
					v-if="
						closeable &&
						(iconPosition === IconPositionEnum['top-left'] ||
							iconPosition === IconPositionEnum['top-right'])
					"
					class="ui-popup-close"
					:class="[iconfontClass, iconPosition, closeIcon]"
					@click="handleCloseClick"
				></i>
			</view>
			<slot></slot>
			<view
				v-if="
					closeable &&
					(iconPosition === IconPositionEnum['bottom-left'] ||
						iconPosition === IconPositionEnum['bottom-right'])
				"
				class="ui-popup-footer"
				:class="[props.iconPosition]"
			>
				<i
					class="ui-popup-close"
					:class="[iconfontClass, iconPosition, closeIcon]"
					@click="handleCloseClick"
				></i>
			</view>
		</view>
	</UiPopupContainer>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import UiPopupContainer from '../ui-popup-container/ui-popup-container.vue'
import { PopupPosition, IconPositionEnum } from './types'
import type { PopupProps } from './types'

defineOptions({
	name: 'UiPopup',
})

const props = withDefaults(defineProps<PopupProps>(), {
	modelValue: false,
	round: false,
	zIndex: 999,
	title: '',
	position: PopupPosition.center,
	maskShow: true,
	isMaskClick: false,
	closeable: true,
	textPosition: 'left',
	iconPosition: IconPositionEnum['top-right'],
	closeIcon: 'icon-uniapp-close-line',
	iconfontClass: 'iconfont-uniapp',
	backgroundType: 'blank',
})

const emits = defineEmits(['update:modelValue', 'open', 'close'])

const popupContainerRef = ref()

const safeAreaInsetTop = computed(() => {
	const hasMenuButton = !!uni.getMenuButtonBoundingClientRect
	const menuButtonInfo = hasMenuButton ? uni.getMenuButtonBoundingClientRect() : {}
	const menuButtonTop = hasMenuButton ? `${menuButtonInfo.top * 2}rpx` : 'env(safe-area-inset-top)'
	return menuButtonTop
})

const uiPopupStyle = computed(() => {
	return { width: props.width, paddingTop: props.position === PopupPosition.top ? safeAreaInsetTop : 0 }
})

watch(
	() => props.modelValue,
	(newValue: boolean) => {
		if (newValue) {
			popupContainerRef.value.open()
		} else {
			popupContainerRef.value.close()
		}
	},
)

const handleCloseClick = () => {
	emits('update:modelValue', false)
}

const handleChange = ({ show }: { show: boolean }) => {
	if (show) {
		emits('open', show)
		return
	}
	emits('close', show)
	emits('update:modelValue', show)
}
</script>
<style scoped lang="scss">
@use './ui-popup.scss';
</style>
