<template>
	<view class="empty-view-wrap" :style="{ marginTop: top }">
		<view class="empty-view">
			<image class="empty-image" :src="icon"></image>
			<view class="empty-desc">{{ desc }}</view>
			<view class="btn-wrap" v-if="showRefresh">
				<UiButton
					type="secondary"
					size="lg"
					width="100%"
					style-name="light"
					text="重试"
					@click="refreshClick"
				></UiButton>
				<view class="back-btn" v-if="showBack">
					<UiButton
						type="normal"
						size="lg"
						width="100%"
						style-name="light"
						text="返回"
						@click="navigateBack"
					></UiButton>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import UiButton from '../ui-button/ui-button.vue'
import emptyIcon from '../../assets/images/components/ui-empty/empty-icon.png'

defineOptions({
	name: 'EmptyView',
})

const props = defineProps({
	// 图标
	icon: {
		type: String,
		default: emptyIcon,
	},
	// 描述
	desc: {
		type: String,
		default: '暂无数据',
	},
	top: {
		type: String,
		default: '15%',
	},
	showRefresh: {
		type: Boolean,
		default: true,
	},
	showBack: {
		type: Boolean,
		default: true,
	},
	backFn: {
		type: Function,
		default: undefined,
	},
})
const emits = defineEmits(['refresh'])
onMounted(() => {})

// 重试按钮点击
const refreshClick = () => {
	emits('refresh')
}

// 导航栏点击返回
const navigateBack = () => {
	if (props.backFn) {
		props.backFn()
	} else {
		uni.navigateBack()
	}
}
</script>
<style lang="scss" scoped>
@use './ui-empty.scss';
</style>
