.setting-cell-wrap {
	width: 100%;
	background: var(--light-list-normal-default-background);
	border-radius: var(--content-default-radius-md);
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	padding: 0 var(--content-default-padding-lg);
	flex: 1;
	.cell-left {
		display: flex;
		flex-direction: row;
		.cell-icon-wrap {
			display: flex;
			flex-direction: row;
			align-items: center;
			.cell-icon {
				margin-right: 12rpx;
			}
		}
		.cell-icon-sm {
			height: 64rpx;
		}
		
		.cell-icon-md {
			height: 88rpx;
		}
		
		.cell-icon-lg {
			height: 112rpx;
		}
		
		.cell-title-wrap {
			display: flex;
			flex-direction: row;
		}
		.cell-title {
			flex: 1;
			color: var(--light-list-normal-default-label);
			font-weight: 500;
		}
		.cell-title-sm {
			font-size: var(--font-body-md);
			padding: 10rpx 0;
		}
		.cell-title-md {
			font-size: var(--font-body-md);
			padding: 22rpx 0;
		}
		.cell-title-lg {
			font-size: var(--font-body-lg);
			padding: 32rpx 0;
		}
		.sub-title-wrap {
			display: flex;
			flex-direction: row;
			align-items: center;
		}
		.cell-subTitle-sm,
		.cell-subTitle-md {
			font-size: var(--font-body-sm);
			color: var(--light-list-normal-default-secondary);
		}
		.cell-subTitle-ld {
			font-size: var(--font-body-md);
			color: var(--light-list-normal-default-secondary);
		}
	}
	.cell-right {
		display: flex;
		flex-direction: row;
		margin-left: 40rpx;
		text-align: right;
		align-items: center;
		justify-content: flex-end;
		gap: var(--form-md-margin);
		flex: 1;
		.cell-value-prefix {
			font-size: var(--font-body-min);
			color: var(--light-list-normal-default-text);
			font-weight: 500;
			margin-top: 8rpx;
		}
		.cell-value-suffix {
			font-size: var(--font-body-md);
			color: var(--light-list-normal-default-text);
			font-weight: 400;
		}
		.cell-value-sm,
		.cell-value-md {
			font-size: var(--font-body-md);
			color: var(--light-list-normal-default-text);
			line-height: 44rpx;
			min-height: 44rpx;
		}
		.cell-value-lg {
			font-size: var(--font-body-lg);
			line-height: 48rpx;
			min-height: 48rpx;
			color: var(--light-list-normal-default-text);
		}
	}
}
.cell-size-sm {
	min-height: 64rpx;
}

.cell-size-md {
	min-height: 88rpx;
}

.cell-size-lg {
	min-height: 112rpx;
}
.background {
	background: var(--light-list-normal-press-background);
}
.transparent {
	background: transparent;
}
.noPadding {
	padding-left: 0;
	padding-right: 0;
}
.line {
	margin: 0 var(--content-default-padding-lg);
	height: 2rpx;
	background: var(--light-list-normal-default-underline);
}
